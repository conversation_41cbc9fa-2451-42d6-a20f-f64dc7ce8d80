import 'dart:convert';

import 'package:flutter/material.dart';

import 'methods.dart';

const greyGrad =
    "/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCABbAJYDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9Jmao2ahmqNmrUyEZqjZqGamM1AxGao2ahmqNmoGDNUbNQzVGzUAIzVGzUM1Rs1MBGao2ahmqJmqgBmqNmoZqiZqABmqNmoZqjZqYCM1Rs1KzVEzVQAWophaigD0lmqNmoZqjZqxJBmqNmoY1GzUFAzVGzUM1Rs1ACM1Rs1DNUTNTAGaomalZqiZqoAZqiZqVmqJmoAGao2ahmqNmpoBGao2ahmqNjVADNUbNQzVGzUAKWoqJmooA9KZqjZqGao2asRCM1Rs1KzVEzUDBmqJmpWaomamAM1RM1DNUbNVADNUTNQzVGzUAIzVGzUM1Rs1MYM1Rs1DNUbNVCEZqYzUM1Rs1AAzVEzUrNUTNTAGaio2aimB6UxqNmoZqjZq5wBmqJmoZqjZqYCM1Rs1DNUbNVAIzVGzUM1RM1AAzVGzUM1Rs1MAZqiZqGao2aqAGao2ahmpjNQAjNUbNQzVGzUAIzVGzUM1Rs1UAM1FRM1FAHpbNUbNQzVEzVgAM1RM1KzVEze9UAM1RM1KzVEze9AAzVEze9KzVEzUwBmqNmoZqjZqYAzVGze9DNUbNTAGao2b3oZqiZvegAZqiZqVmqJmqgBmqJm96VmqJmoAGaio2aigD0tmqJmpWqNqxARmqJmpWqNqYCM1RM1K1RmgBGaomanNUTVQCM1Rs1K3eo2pgDNUTNTmqJqAEZqiZqVqjaqARmqJmpWqJqAEZveo2albvUbUAIzUVG1FAH//Z";

final placeholderGrad = base64.decode(greyGrad);

class ExamTypes {
  static const online = "Online";
  static const offline = "Offline";
}

class DevelopedByDefault extends StatelessWidget {
  const DevelopedByDefault({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        text: "Developed by ",
        style: const TextStyle(
          color: Colors.black,
        ),
        children: [
          rMediaText(),
        ],
      ),
    );
  }
}

class PayRespReturn {
  final String orderId;
  final String msg;

  PayRespReturn(this.orderId, this.msg);
}
