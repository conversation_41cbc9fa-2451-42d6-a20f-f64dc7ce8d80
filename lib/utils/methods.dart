import 'dart:math';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wellfed/utils/firebase.dart';
// ignore: depend_on_referenced_packages
import 'package:intl/intl.dart';

import 'consts.dart';
import 'theme.dart';

bool isLoggedIn() => FBAuth.auth.currentUser != null;
// bool isLoggedIn() => Get.find<HomeController>().loggedIn;

extension MetaWid on DateTime {
  String goodDate() {
    try {
      return DateFormat.yMMMM().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDayDate() {
    try {
      return DateFormat.yMMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodTime() {
    try {
      return DateFormat('hh:mm a').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }
}

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomId(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

showAppSnackBar(BuildContext context, String title, String msg,
    [ContentType? contentType]) async {
  try {
    final snackBar = SnackBar(
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      content: AwesomeSnackbarContent(
        title: title,
        message: msg,
        contentType: contentType ?? ContentType.success,
      ),
    );
    ScaffoldMessenger.of(context)
      ..hideCurrentSnackBar()
      ..showSnackBar(snackBar);
    return;
  } catch (e) {
    debugPrint(e.toString());
  }
}

TextSpan rMediaText() {
  return TextSpan(
    text: "R Media",
    style: appTextStyleOne.copyWith(
      fontWeight: FontWeight.bold,
      color: Colors.redAccent,
    ),
    recognizer: TapGestureRecognizer()..onTap = onRMTap,
  );
}

onRMTap() async {
  try {
    final url = Uri.parse(rMediaUrl);
    if (await canLaunchUrl(url)) {
      launchUrl(url);
    }
  } catch (e) {
    debugPrint(e.toString());
  }
}
