import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/models/checkout_model.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/views/auth/login.dart';
import 'package:wellfed/views/auth/register.dart';
import 'package:wellfed/views/cart/cart.dart';
import 'package:wellfed/views/checkout/checkout.dart';
import 'package:wellfed/views/course/course.dart';
import 'package:wellfed/views/courses/courses.dart';
import 'package:wellfed/views/home/<USER>';
import 'package:wellfed/views/learning/learning.dart';
import 'package:wellfed/views/mycourses/Exams/exams.dart';
import 'package:wellfed/views/mycourses/Offline/offline_qr.dart';
import 'package:wellfed/views/mycourses/certificates/certificates.dart';
import 'package:wellfed/views/mycourses/dashboard/dashboard.dart';
import 'package:wellfed/views/mycourses/my_courses.dart';
import 'package:wellfed/views/mycourses/users/users.dart';
import 'package:wellfed/views/profile/profile.dart';
import 'package:wellfed/views/webview/webview.dart';
import 'package:wellfed/widgets/error_page.dart';
import '../views/certificate/certificate.dart';

final _shellNavigatorKey = GlobalKey<NavigatorState>();
final _shellNavigatorKey2 = GlobalKey<NavigatorState>();
final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: Routes.home,
  // initialLocation: Routes.home,
  routes: _routes,
  redirect: redirector,
  errorBuilder: (context, state) => const ErrorPage(),
);

FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  if (isLoggedIn() &&
      (state.fullPath == Routes.login || state.fullPath == Routes.register)) {
    return Routes.home;
  }
  return null;
}

List<RouteBase> get _routes {
  return <RouteBase>[
    // Home Routes
    StatefulShellRoute(
      builder: (context, state, navigationShell) =>
          HomePage(child: navigationShell),
      branches: [
        // first branch (A)
        StatefulShellBranch(
          navigatorKey: _shellNavigatorKey,
          routes: [
            // top route inside branch
            GoRoute(
              path: Routes.home,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  NoTransitionPage(
                      child: WebViewWid(
                          widname: state.extra != null
                              ? state.extra as String
                              : null)),
            ),
            GoRoute(
              path: Routes.courses,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  const NoTransitionPage(child: Courses()),
            ),
            GoRoute(
              path: Routes.profile,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  const NoTransitionPage(child: ProfilePage()),
            ),
            GoRoute(
              path: Routes.cart,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  const NoTransitionPage(child: CartPage()),
            ),
            GoRoute(
              path: '${Routes.course}/:id',
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  NoTransitionPage(
                      child: CourseDetailsPage(
                courseDocId: state.pathParameters['id'],
              )),
            ),
            GoRoute(
              path: Routes.checkout,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  NoTransitionPage(
                      child: CheckoutPage(
                          checkoutModel: state.extra != null
                              ? state.extra as CheckoutModel
                              : null)),
            ),
          ],
        ),
      ],
      navigatorContainerBuilder: (BuildContext context,
          StatefulNavigationShell navigationShell, List<Widget> children) {
        return children[navigationShell.currentIndex];
      },
    ),
    // My Courses routes
    StatefulShellRoute(
      builder: (context, state, navigationShell) =>
          MyCourses(child: navigationShell),
      branches: [
        StatefulShellBranch(
          navigatorKey: _shellNavigatorKey2,
          routes: [
            GoRoute(
              path: Routes.mycourses,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  const NoTransitionPage(child: MyDashBoard()),
            ),
            GoRoute(
              path: Routes.certificates,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  const NoTransitionPage(child: CertificatesPage()),
            ),
            GoRoute(
              path: Routes.exams,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  const NoTransitionPage(child: ExamPage()),
            ),
            GoRoute(
              path: Routes.offline,
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  const NoTransitionPage(child: OfflineQrPage()),
            ),
            GoRoute(
              path: '${Routes.users}/:id',
              pageBuilder: (BuildContext context, GoRouterState state) =>
                  NoTransitionPage(
                      child: EUsers(
                branchId: state.pathParameters['id'],
              )),
            ),
          ],
        ),
      ],
      navigatorContainerBuilder: (BuildContext context,
          StatefulNavigationShell navigationShell, List<Widget> children) {
        return children[navigationShell.currentIndex];
      },
    ),
    GoRoute(
      path: '${Routes.learn}/:id',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: LearningPage(
        userCourseId: state.pathParameters['id'] ?? "",
      )),
    ),
    GoRoute(
      path: Routes.login,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: LoginPage()),
    ),
    GoRoute(
      path: Routes.register,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: RegisterPage(
                  reg: state.extra != null ? state.extra as bool : false)),
    ),
    GoRoute(
      path: '${Routes.certi}/:cId',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: CertiPageExt(
        cId: state.pathParameters['cId'] ?? "",
      )),
    ),
  ];
}

class Routes {
  static const home = "/home";
  static const courses = "/courses";
  static const login = "/login";
  static const register = "/register";
  static const profile = "/profile";
  static const cart = "/cart";
  static const course = "/course";
  static const checkout = "/checkout";
  static const mycourses = "/mycourses";
  static const certificates = "/certificates";
  static const exams = "/exam-schedules";
  static const offline = "/offline-qr";
  static const learn = "/learn";
  static const users = "/users";
  static const certi = "/certi";
}
