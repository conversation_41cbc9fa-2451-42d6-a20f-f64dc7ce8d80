import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:webviewx/webviewx.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/router.dart';

import '../models/cart_model.dart';
import '../models/e_branch_model.dart';
import '../models/user_model.dart';
import '../utils/other.dart';

final homeScafKey = GlobalKey<ScaffoldState>();
final myCoursesScafKey = GlobalKey<ScaffoldState>();
final learningScafKey = GlobalKey<ScaffoldState>();

class HomeController extends GetxController {
  ScrollController scrlCtrl = ScrollController();
  WebViewXController? webviewController;
  bool loggedIn = false;
  bool isEnterprise = false;
  bool courseLoaded = false;
  List<CourseModel> courseList = <CourseModel>[];
  List<CartCourseModel> cartList = <CartCourseModel>[];
  List<UserCourseModel> myCourses = <UserCourseModel>[];
  List<EBranch> branches = [];
  final searchCtrl = TextEditingController();
  String selectedCat = categoryTypes.first;
  double? cartTotal = 0;
  UserModel? userData;
  String? selectedId; // "wZ0iOG"; // TODO: make null || Dont assign
  List<String> selectedIdList = <String>[];

  @override
  void onInit() {
    super.onInit();
    userStream();
    setUpCoursesStream();
  }

  userStream() async {
    FirebaseAuth.instance.authStateChanges().listen((event) {
      loggedIn = event != null;
      debugPrint(event?.uid);
      if (loggedIn) {
        setUpUserDataStream();
        setUpMyCoursesStream();
      } else {
        myCourses.clear();
      }
    });
  }

  setUpUserDataStream() async {
    final stream =
        FBFireStore.users.doc(FBAuth.auth.currentUser!.uid).snapshots();
    stream.listen((event) {
      userData = event.data() != null
          ? UserModel.fromJson(event.id, event.data()!)
          : null;
      if (userData != null) {
        isEnterprise = userData?.eId != null;
        syncCartItems();
      }
      update();
    });
  }

  setUpCoursesStream() async {
    FBFireStore.courses
        .where('blocked', isEqualTo: false)
        .snapshots()
        .listen((event) {
      courseList =
          event.docs.map((e) => CourseModel.fromJson(e.id, e.data())).toList();
      courseLoaded = true;
      calculateCartTotal();
    });
  }

  setUpMyCoursesStream() async {
    FBFireStore.userCourses
        .where('uid', isEqualTo: FBAuth.auth.currentUser?.uid)
        .where('blocked', isEqualTo: false)
        .snapshots()
        .listen((event) {
      myCourses = event.docs
          .map((e) => UserCourseModel.fromJson(e.id, e.data()))
          .toList();
      myCourses.sort((a, b) => b.time?.compareTo(a.time!) ?? 0);
      update();
    });
  }

  syncCartItems() async {
    try {
      final cartData = userData?.cartItems ?? [];
      cartData.sort((a, b) => a.courseId.compareTo(b.courseId));
      cartList = cartData;
      calculateCartTotal();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  addToDBCart(CartCourseModel item) async {
    try {
      await FBFireStore.users
          .doc(FBAuth.auth.currentUser?.uid)
          .update({'cartItems.${item.courseId}': item.qty});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  removeFromDBCart(CartCourseModel item) async {
    try {
      await FBFireStore.users
          .doc(FBAuth.auth.currentUser?.uid)
          .update({'cartItems.${item.courseId}': FieldValue.delete()});
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  addToCart(BuildContext context, CartCourseModel cartCourse) {
    if (!isLoggedIn()) {
      context.go(Routes.login);
      return;
    }
    if (cartList.contains(cartCourse)) {
      context.go(Routes.cart);
      return;
    }
    // cartList.add(cartCourse);
    addToDBCart(cartCourse);
    showAppSnackBar(context, "Congratulations",
        "Course was successfully add to your cart!");
    calculateCartTotal();
    update();
  }

  removeFromCart(String courseDocId) {
    try {
      removeFromDBCart(
          cartList.firstWhere((element) => element.courseId == courseDocId));
      // cartList.removeWhere((element) => element.courseId == courseDocId);
      calculateCartTotal();
      update();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  addQtyToCourse(String courseDocId) async {
    final item =
        cartList.firstWhereOrNull((element) => element.courseId == courseDocId);
    if (item != null) {
      // item.qty++;
      await FBFireStore.users
          .doc(FBAuth.auth.currentUser?.uid)
          .update({'cartItems.${item.courseId}': FieldValue.increment(1)});
      // calculateCartTotal();
      update();
    }
  }

  subtractQtyToCourse(String courseDocId) async {
    final item =
        cartList.firstWhereOrNull((element) => element.courseId == courseDocId);
    if (item != null) {
      if (item.qty > 1) {
        // item.qty--;
        // calculateCartTotal();
        await FBFireStore.users
            .doc(FBAuth.auth.currentUser?.uid)
            .update({'cartItems.${item.courseId}': FieldValue.increment(-1)});
        update();
      }
    }
  }

  calculateCartTotal() {
    double total = 0;
    for (var element in cartList) {
      final crse =
          courseList.firstWhereOrNull((e) => e.docId == element.courseId);
      if (crse != null) {
        if (isEnterprise && element.qty >= crse.bulkMinQty) {
          total += crse.bulkPrice * element.qty;
        } else {
          total += crse.price * element.qty;
        }
      } else {
        cartTotal = null;
      }
    }
    if (total != 0) cartTotal = total;
    update();
  }

  // LEARNING PAGE //
  setSelectedId(String? id) {
    selectedId = id;
    update();
  }

  setSelectedIdList(List<String> lst) {
    selectedIdList = lst;
    update();
  }

  Future<PayRespReturn?> onCheckout(
      BuildContext context,
      List<CartCourseModel> checkoutCourses,
      double total,
      double discount,
      double original,
      bool fromCart) async {
    try {
      final orderDoc = await FBFireStore.orders.add({
        "uid": FBAuth.auth.currentUser!.uid,
        "checkoutCourses": checkoutCourses
            .map((e) => {
                  'courseId': e.courseId,
                  'qty': e.qty,
                })
            .toList(),
        "totalAmount": total,
        "discount": discount,
        "fromCart": fromCart,
        "original": original,
        "processed": false,
        "time": FieldValue.serverTimestamp(),
      });
      List lineItems = [];
      for (var checkoutCrse in checkoutCourses) {
        final crse = courseList
            .firstWhereOrNull((e) => e.docId == checkoutCrse.courseId);
        if (crse == null) return null;
        lineItems.add({
          "price_data": {
            "currency": 'usd',
            "product_data": {
              "name": crse.title,
            },
            "unit_amount": isEnterprise && checkoutCrse.qty >= crse.bulkMinQty
                ? crse.bulkPrice * 100
                : crse.price * 100,
          },
          "quantity": checkoutCrse.qty,
        });
      }
      if (lineItems.length != checkoutCourses.length) {
        if (context.mounted) {
          showAppSnackBar(context, "Ops", "Something went wrong!");
        }
        return null;
      }
      // final result = await FBFunctions.ff
      //     .httpsCallableFromUrl(
      //         'http://127.0.0.1:5001/wellfed-9384f/us-central1/checkout')
      //     .call(
      final result = await FBFunctions.ff.httpsCallable('checkout').call(
        {
          "list": lineItems,
          "orderId": orderDoc.id,
          "email": userData?.email,
        },
      );
      final response = result.data;
      debugPrint(response.toString());
      // final url = Uri.parse(response['msg']);
      // if (await canLaunchUrl(url)) {
      //   final launched = await launchUrl(url,
      //       mode: LaunchMode.externalApplication,
      //       webOnlyWindowName: "WellFed",
      //       webViewConfiguration:
      //           const WebViewConfiguration(enableJavaScript: true));
      // return launched ? PayRespReturn(orderDoc.id, response['msg']) : null;
      return response != null
          ? PayRespReturn(orderDoc.id, response['msg'])
          : null;
      // }
      // return null;
      // await buyCourse(orderDoc.id);
      // if (context.mounted) {
      //   showAppSnackBar(
      //       context, "Congratulations!", "Your purchase was successful!");
      // }
    } catch (e) {
      debugPrint(e.toString());
      if (context.mounted) {
        showAppSnackBar(context, "Ops!",
            "Something went wrong, please try after some time!");
      }
      return null;
    }
  }
}
/* 
buyCourse(String orderId) async {
  try {
    final orderDoc = await FBFireStore.orders.doc(orderId).get();
    final userDoc = await FBFireStore.users.doc(orderDoc['uid']).get();
    bool isEnterprise = userDoc['eId'] != null;
    final batch = FBFireStore.fs.batch();
    num totalSaleVal = 0;
    num totalCrseQty = 0;
    for (var i = 0; i < orderDoc['checkoutCourses'].length; i++) {
      final courseDoc = await FBFireStore.courses
          .doc(orderDoc['checkoutCourses'][i]['courseId'])
          .get();
      int qtySold = orderDoc['checkoutCourses'][i]['qty'];
      num saleValue = isEnterprise
          ? (qtySold >= courseDoc['bulkMinQty']
              ? courseDoc['bulkPrice'] * qtySold
              : courseDoc['price'] * qtySold)
          : courseDoc['price'];
      // Updating Course flags
      batch.update(FBFireStore.courses.doc(courseDoc.id), {
        "qtySold": FieldValue.increment(qtySold),
        "valueSold": FieldValue.increment(saleValue),
      });
      totalSaleVal += saleValue;
      totalCrseQty += qtySold;
      // Create User Course Document
      batch.set(FBFireStore.userCourses.doc(), {
        "courseId": courseDoc.id,
        "isEnterprise": isEnterprise,
        "uid": userDoc.id,
        "orderId": orderId,
        "assignedByUid": null,
        "assignedByName": null,
        "blocked": false,
        "startDate": DateTime.now(),
        "endDate": DateTime.now().add(Duration(days: courseDoc['days'])),
        "examSchedules": {},
        "certiUrl": null,
        "score": null,
        "progress": {},
        "asignScores": {},
        "qty": orderDoc['checkoutCourses'][i]['qty'],
        "assigned": 0,
        "completed": false,
        "time": FieldValue.serverTimestamp(),
      });
    }
    // Updating Users Document
    batch.update(FBFireStore.users.doc(userDoc.id), {
      if (orderDoc['fromCart'] == true) "cartItems": {},
      "qtyPurchased": FieldValue.increment(totalCrseQty),
      "valuePurchased": FieldValue.increment(totalSaleVal),
    });
    await batch.commit();
  } catch (e) {
    debugPrint(e.toString());
  }
}
 */