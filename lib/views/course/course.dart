import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/responsive.dart';
import 'package:wellfed/utils/theme.dart';
import 'package:wellfed/views/course/buy_box.dart';
import '../../models/cart_model.dart';

class CourseDetailsPage extends StatelessWidget {
  const CourseDetailsPage({super.key, required this.courseDocId});

  final String? courseDocId;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<CourseModel>(
      stream: FBFireStore.courses
          .doc(courseDocId)
          .snapshots()
          .map((event) => CourseModel.fromJson(event.id, event.data()!)),
      builder: (BuildContext context, snapshot) {
        if (snapshot.hasError) {
          debugPrint(snapshot.error.toString());
          return const Center(
              child: Text("Course Not Found",
                  style: TextStyle(fontWeight: FontWeight.bold)));
        }
        if (snapshot.hasData) {
          return snapshot.data == null
              ? const Center(
                  child: Text("Course Not Found",
                      style: TextStyle(fontWeight: FontWeight.bold)))
              : ResponsiveWid(
                  mobile: CourseDataWids(small: true, course: snapshot.data!),
                  tablet: CourseDataWids(small: true, course: snapshot.data!),
                  desktop: CourseDataWids(small: false, course: snapshot.data!),
                );
        }
        return Center(
            child: loaderWave(color: Theme.of(context).primaryColor, size: 30));
      },
    );
  }
}

class CourseDataWids extends StatelessWidget {
  const CourseDataWids({super.key, required this.small, required this.course});
  final bool small;
  final CourseModel course;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final padding = small
        ? const EdgeInsets.all(20)
        : EdgeInsets.symmetric(horizontal: size.width * .1, vertical: 20);
    return SingleChildScrollView(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      course.title,
                      style: appTitleTextStyle.copyWith(
                          fontWeight: FontWeight.bold, fontSize: 30),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 2.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            course.desc,
                            style: appTextStyleTwo.copyWith(fontSize: 20),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            'By ${course.author}',
                            style: TextStyle(
                                color: Colors.grey.shade700, fontSize: 18),
                          ),
                          const SizedBox(height: 2),
                          Text.rich(
                            TextSpan(
                              text: "Last Updated on ",
                              style: TextStyle(
                                  color: Colors.green.shade700, fontSize: 16),
                              children: [
                                TextSpan(
                                  text: course.updatedOn.goodDate(),
                                  style: TextStyle(
                                      color: Colors.green.shade800,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              const Icon(CupertinoIcons.timer, size: 18),
                              const SizedBox(width: 2),
                              Text.rich(
                                TextSpan(
                                  text: '${course.duration.hours} ',
                                  style: TextStyle(
                                      color: Colors.grey.shade900,
                                      fontSize: 16),
                                  children: [
                                    TextSpan(
                                      text: "Hours ",
                                      style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 16),
                                    ),
                                    TextSpan(
                                      text: '${course.duration.minutes} ',
                                      style: TextStyle(
                                          color: Colors.grey.shade900,
                                          fontSize: 16),
                                    ),
                                    TextSpan(
                                      text: "Minutes",
                                      style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Icon(CupertinoIcons.play_rectangle,
                                  size: 18),
                              const SizedBox(width: 4),
                              Text.rich(
                                TextSpan(
                                  text: '${course.chapters.length} ',
                                  style: TextStyle(
                                      color: Colors.grey.shade900,
                                      fontSize: 16),
                                  children: [
                                    TextSpan(
                                      text: "Chapters ",
                                      style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 16),
                                    ),
                                    TextSpan(
                                      text:
                                          '${course.chapters.map((e) => e.modules.length).reduce((value, element) => value + element)} ',
                                      style: TextStyle(
                                          color: Colors.grey.shade900,
                                          fontSize: 16),
                                    ),
                                    TextSpan(
                                      text: "Lectures",
                                      style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Text.rich(
                            TextSpan(
                              text: course.examType,
                              style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontSize: 16),
                              children: const [
                                TextSpan(
                                  text: " Exam",
                                  style: TextStyle(
                                      color: Colors.black, fontSize: 16),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    GetBuilder<HomeController>(
                      init: Get.find<HomeController>(),
                      builder: (_) {
                        return Row(
                          children: [
                            if (_.isEnterprise && course.bulkMinQty != 1)
                              Container(
                                margin: const EdgeInsets.only(right: 6),
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(.1),
                                    borderRadius: BorderRadius.circular(8)),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(4)),
                                      child: Text(
                                        '\$${course.bulkPrice}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ),
                                    Text(
                                      "For ${course.bulkMinQty - 1}+",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            MaterialButton(
                              padding: const EdgeInsets.all(14),
                              color: const Color.fromARGB(255, 233, 246, 249),
                              hoverColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                  side: BorderSide(
                                      color: appColorOne.withOpacity(.3)),
                                  borderRadius: BorderRadius.circular(12)),
                              onPressed: () {
                                _.addToCart(context,
                                    CartCourseModel(courseId: course.docId));
                              },
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const SizedBox(width: 24),
                                    _.cartList.contains(CartCourseModel(
                                            courseId: course.docId))
                                        ? Icon(CupertinoIcons.bag,
                                            color: appColorOne.withOpacity(.8))
                                        : Icon(CupertinoIcons.bag_badge_plus,
                                            color: appColorOne.withOpacity(.8)),
                                    const SizedBox(width: 8),
                                    Text(
                                      '${_.cartList.contains(CartCourseModel(courseId: course.docId)) ? "Go" : "Add"} to Cart',
                                      style:
                                          const TextStyle(color: appColorOne),
                                    ),
                                    const SizedBox(width: 24),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                          color: appColorOne.withOpacity(.08),
                                          borderRadius:
                                              BorderRadius.circular(8)),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            '\$${course.discountPrice}',
                                            style: TextStyle(
                                                color: Colors.grey.shade700,
                                                fontSize: 12,
                                                decoration:
                                                    TextDecoration.lineThrough),
                                          ),
                                          Text(
                                            _.isEnterprise &&
                                                    course.bulkMinQty == 1
                                                ? '\$${course.bulkPrice}'
                                                : '\$${course.price}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              // color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    const SizedBox(height: 20),
                    Text(
                      "Note: This course must be successfully finished within  ${course.days} Days from the date of ${isLoggedIn() ? Get.find<HomeController>().isEnterprise ? "assigning" : "purchase" : "assigning/ purchase"}.",
                      style: const TextStyle(
                          // fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ),
              if (!small) const SizedBox(width: 20),
              if (!small)
                Expanded(
                  child: BuyBox(course: course, large: !small),
                ),
            ],
          ),
          if (small) const SizedBox(height: 20),
          if (small) BuyBox(course: course, large: !small),
          const SizedBox(height: 20),
          Text(
            "Overview",
            style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800),
          ),
          const Divider(),
          Text(
            course.overview,
            textAlign: TextAlign.justify,
            style: appTextStyleOne.copyWith(fontSize: 15),
          ),
          const SizedBox(height: 40),
          // Text(
          //   "Content",
          //   style: TextStyle(
          //       fontSize: 18,
          //       fontWeight: FontWeight.bold,
          //       color: Colors.grey.shade800),
          // ),
          // const SizedBox(height: 18),
          // ExpandableChapters(chapters: course.chapters),
          // const SizedBox(height: 40),
        ],
      ),
    );
  }
}
