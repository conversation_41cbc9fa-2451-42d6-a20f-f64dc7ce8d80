import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/models/cart_model.dart';
import 'package:wellfed/models/checkout_model.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/router.dart';
import '../../models/course_model.dart';
import '../../utils/other.dart';
import '../../utils/theme.dart';

class BuyBox extends StatelessWidget {
  const BuyBox({
    super.key,
    required this.course,
    required this.large,
  });
  final CourseModel course;
  final bool large;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    bool small = size.width < mobileMinSize2;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: small || large
            ? Column(
                children: [
                  _ImageBox(course: course),
                  _Content(course: course),
                ],
              )
            : Row(
                children: [
                  Expanded(
                      child: LimitedBox(
                          maxHeight: 200, child: _ImageBox(course: course))),
                  const SizedBox(width: 20),
                  Expanded(child: _Content(course: course)),
                ],
              ),
      ),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content({
    required this.course,
  });

  final CourseModel course;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 6),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Certificate on successful completion of ${course.chapters.map((e) => e.assignments.length).reduce((value, element) => value + element)} assignments",
                  style: const TextStyle(fontWeight: FontWeight.w100),
                ),
                const SizedBox(height: 4),
                Text(
                  "Certificate is valid for ${course.certiValidity} Days",
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
          Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 42,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                        backgroundColor: appColorOne,
                        shape: ContinuousRectangleBorder(
                            borderRadius: BorderRadius.circular(20))),
                    onPressed: () {
                      if (!isLoggedIn()) {
                        context.go(Routes.login);
                        return;
                      }
                      context.go(Routes.checkout,
                          extra: CheckoutModel(checkoutCourses: [
                            CartCourseModel(courseId: course.docId)
                          ], enableEdit: true));
                    },
                    child: const Text(
                      "Buy Now",
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
        ],
      ),
    );
  }
}

class _ImageBox extends StatelessWidget {
  const _ImageBox({
    required this.course,
  });

  final CourseModel course;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: SizedBox.expand(
          child: FadeInImage.memoryNetwork(
              placeholderErrorBuilder: (context, error, stackTrace) =>
                  Image.memory(placeholderGrad),
              placeholder: placeholderGrad,
              fit: BoxFit.cover,
              image: course.imageUrl),
        ),
      ),
    );
  }
}
