import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:webviewx/webviewx.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/responsive.dart';
import 'package:wellfed/utils/router.dart';
import 'package:wellfed/views/webview/webview.dart';
import '../../controllers/home_ctrl.dart';
import '../../utils/consts.dart';
import '../../utils/theme.dart';

class WellFedAppbar extends StatelessWidget implements PreferredSizeWidget {
  const WellFedAppbar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return const WebViewAware(
      child: ResponsiveWid(
        mobile: _Small(),
        tablet: _Small(),
        desktop: _Large(),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kIsWeb ? 80 : 60);
}

class _Small extends StatelessWidget {
  const _Small();

  @override
  Widget build(BuildContext context) {
    final loggedIn = isLoggedIn();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0),
      decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6)]),
      child: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                IconButton(
                    onPressed: () {
                      homeScafKey.currentState?.openDrawer();
                    },
                    icon: const Icon(CupertinoIcons.line_horizontal_3)),
                const SizedBox(width: 12),
                _logo(context),
                const Spacer(),
                // _largeRegisterButton(context),
                if (!loggedIn) _largeLoginButton(context),
                if (loggedIn) _cartButton(context),
                if (loggedIn) _profileButton(context),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _Large extends StatelessWidget {
  const _Large();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    final loggedIn = isLoggedIn();
    return Container(
      padding: !large
          ? EdgeInsets.symmetric(
              horizontal: small ? 28 : size.width * .1, vertical: 16)
          : homeLargeInsects(size, horizontal: size.width * .114, vertical: 16),
      // padding: const EdgeInsets.symmetric(horizontal: 40.0, vertical: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6)],
      ),
      child: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                _logo(context),
                const Spacer(),
                _buttonRow(context),
                const Spacer(flex: 6),
                // if (!loggedIn) _largeRegisterButton(context),
                if (!loggedIn) _largeLoginButton(context),
                if (loggedIn) _cartButton(context),
                if (loggedIn) _profileButton(context),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

TextButton _largeRegisterButton(BuildContext context) => TextButton(
    style: _appbarButtonStyle(),
    onPressed: () => context.go(Routes.register),
    child: const Text("Register"));

ElevatedButton _largeLoginButton(BuildContext context) => ElevatedButton(
    style: ButtonStyle(
      elevation: const MaterialStatePropertyAll(5),
      padding: const MaterialStatePropertyAll(
          EdgeInsets.symmetric(horizontal: 24, vertical: 18)),
      shape: MaterialStatePropertyAll(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(45))),
    ),
    onPressed: () => context.go(Routes.login),
    child: const Text("Let's Begin"));

Row _buttonRow(BuildContext context) {
  return Row(
    children: [
      TextButton(
          style: _appbarButtonStyle(),
          onPressed: () => context.go(Routes.home),
          child: Text(
            "Home",
            style: _appBarTextStyle(),
          )),
      TextButton(
          style: _appbarButtonStyle(),
          onPressed: () => context.go(Routes.courses),
          child: Text(
            "Courses",
            style: _appBarTextStyle(),
          )),
      TextButton(
          style: _appbarButtonStyle(),
          onPressed: () async {
            if (appRouter.location != Routes.home) {
              context.go(Routes.home, extra: "entr");
            }
            homeScroll("entr");
            // scrollTo(5000);
          },
          child: Text(
            "For Enterprise",
            style: _appBarTextStyle(),
          )),
      TextButton(
          style: _appbarButtonStyle(),
          onPressed: () async {
            if (appRouter.location != Routes.home) {
              context.go(Routes.home, extra: 'prc');
            }
            homeScroll("prc");
            // scrollTo(6000);
          },
          child: Text(
            "Our Process",
            style: _appBarTextStyle(),
          )),
      TextButton(
          style: _appbarButtonStyle(),
          onPressed: () =>
              context.go(isLoggedIn() ? Routes.mycourses : Routes.login),
          child: Text(
            "My Courses",
            style: _appBarTextStyle(),
          )),
    ],
  );
}

Widget _logo(BuildContext context) => LimitedBox(
    maxWidth: 120,
    child: InkWell(
        hoverColor: Colors.transparent,
        onTap: () => context.go(Routes.home),
        child: Image.asset('assets/logo.png')));

ButtonStyle _appbarButtonStyle() {
  return ButtonStyle(
      foregroundColor: MaterialStateProperty.resolveWith<Color?>(
        (Set<MaterialState> states) {
          if (states.contains(MaterialState.hovered)) {
            return appColorOne;
          }
          if (states.contains(MaterialState.focused) ||
              states.contains(MaterialState.pressed)) {
            return appColorTwo;
          }
          return null;
        },
      ),
      padding: const MaterialStatePropertyAll(
          EdgeInsets.symmetric(horizontal: 24, vertical: 20)),
      shape: MaterialStatePropertyAll(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(45))));
}

TextStyle _appBarTextStyle() => const TextStyle(fontSize: 15);
/* 
void scrollTo(String name) {
  try {
    final ctrl = Get.find<HomeController>().webviewController;
    if (ctrl != null) {
      ctrl.scrollTo(0, yPos);
    }
  } catch (e) {
    debugPrint(e.toString());
  }
} */ /* void scrollTo(int yPos) {
  try {
    final ctrl = Get.find<HomeController>().webviewController;
    if (ctrl != null) {
      ctrl.scrollTo(0, yPos);
    }
  } catch (e) {
    debugPrint(e.toString());
  }
} */

Widget _cartButton(BuildContext context) {
  return GetBuilder<HomeController>(
    init: Get.find<HomeController>(),
    builder: (_) {
      final count = Get.find<HomeController>().cartList.length;
      return Badge(
        offset: Offset.fromDirection(.5, 1.2),
        isLabelVisible: count != 0,
        backgroundColor: appColorOne,
        label: Text('$count'),
        child: IconButton(
          onPressed: () => context.go(Routes.cart),
          icon: Icon(
            CupertinoIcons.bag,
            color: Colors.grey.shade700,
            size: 28,
          ),
        ),
      );
    },
  );
}

Widget _profileButton(BuildContext context) {
  return IconButton(
    onPressed: () => context.go(Routes.profile),
    icon: Icon(
      CupertinoIcons.person_alt_circle,
      color: Colors.grey.shade700,
      size: 28,
    ),
  );
}
