import 'package:flutter/material.dart';
import 'package:wellfed/utils/theme.dart';
import '../../utils/methods.dart';

class CopyRightBar extends StatelessWidget {
  const CopyRightBar({super.key, required this.size});
  final Size size;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color.fromARGB(255, 27, 27, 28),
      padding: EdgeInsets.symmetric(
          horizontal: size.width * .04, vertical: size.width * .02),
      child: Row(
        children: [
          Expanded(
            child: Text.rich(
              TextSpan(
                text: "© 2023 Well Fed | Developed by ",
                style: appTextStyleOne.copyWith(
                  // fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                children: [
                  rMediaText(),
                  // TextSpan(
                  //   text: " & ",
                  //   style: appTextStyleOne.copyWith(
                  //     // fontWeight: FontWeight.bold,
                  //     color: Colors.white,
                  //   ),
                  // ),
                  // TextSpan(
                  //   text: "Digital Cruze",
                  //   style: appTextStyleOne.copyWith(
                  //     color: Colors.orangeAccent.shade700,
                  //     fontWeight: FontWeight.bold,
                  //   ),
                  //   recognizer: TapGestureRecognizer()..onTap = _onDCTap,
                  // ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _onDCTap() {}
}
