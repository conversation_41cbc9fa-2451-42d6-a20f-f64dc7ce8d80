import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../utils/consts.dart';
import '../../utils/methods.dart';
import '../../utils/router.dart';
import '../../utils/theme.dart';
import 'webview.dart';

class ExamLocations extends StatelessWidget {
  const ExamLocations({super.key, required this.size});
  final Size size;

  @override
  Widget build(BuildContext context) {
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    return Container(
      color: const Color.fromARGB(255, 27, 27, 28),
      padding: !large
          ? EdgeInsets.symmetric(
              horizontal: small ? 28 : size.width * .1,
              vertical: small ? 34 : size.width * .06)
          : homeLargeInsects(size),
      child: Column(
        children: [
          _textOne(),
          _textTwo(),
          const SizedBox(height: 12),
          !small
              ? const Row(
                  children: [
                    Expanded(
                      child: Wrap(
                        runAlignment: WrapAlignment.center,
                        alignment: WrapAlignment.center,
                        crossAxisAlignment: WrapCrossAlignment.center,
                        children: [
                          LocationBox(),
                          LocationBox(),
                          LocationBox(),
                        ],
                      ),
                    ),
                  ],
                )
              : Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: !small && !large ? 0.0 : size.width * .1),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      LocationBox(),
                      LocationBox(),
                      LocationBox(),
                    ],
                  ),
                ),
          const SizedBox(height: 20),
          _button(context),
        ],
      ),
    );
  }

  ElevatedButton _button(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: Theme.of(context).primaryColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(4))),
      onPressed: () =>
          context.go(isLoggedIn() ? Routes.mycourses : Routes.login),
      child: const Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 18),
        child: Text(
          "Schedule",
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Text _textTwo() {
    return Text.rich(
      TextSpan(
          text: "Offline Exam ",
          style: appTextStyleTwo.copyWith(
            fontSize: 34,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            // fontFamily: 'Roboto',
          ),
          children: [
            TextSpan(
              text: "Locations ",
              style: appTextStyleTwo.copyWith(
                  fontSize: 34,
                  color: appColorOne,
                  fontWeight: FontWeight.normal),
            ),
          ]),
      textAlign: TextAlign.center,
    );
  }

  Text _textOne() {
    return const Text(
      "Better Learning. Better Results",
      style: TextStyle(
          letterSpacing: 2,
          wordSpacing: 1.5,
          color: appColorOne,
          // color: Theme.of(context).primaryColor,
          fontSize: 15,
          fontFamily: 'Roboto',
          fontWeight: FontWeight.bold),
    );
  }
}

class LocationBox extends StatelessWidget {
  const LocationBox({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color.fromARGB(255, 236, 237, 239),
      margin: const EdgeInsets.all(20),
      constraints: const BoxConstraints(maxWidth: 260),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 140,
            child: Row(
              children: [
                Expanded(
                  child: Image.asset(
                    'assets/map.png',
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                const Text(
                  "Location Name",
                  style: TextStyle(
                      letterSpacing: 1.2,
                      fontSize: 18,
                      fontFamily: 'Roboto',
                      fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 2),
                Text(
                  "Some more detailed information about this location here.",
                  style: appTextStyleTwo.copyWith(),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
