import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webviewx/webviewx.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/views/webview/certification.dart';
import 'package:wellfed/views/webview/copyright.dart';
import 'package:wellfed/views/webview/enterprise.dart';
import 'package:wellfed/views/webview/featured.dart';
import 'package:wellfed/views/webview/footer.dart';
import 'package:wellfed/views/webview/locations.dart';
import 'package:wellfed/views/webview/our_process.dart';
import 'header.dart';

final _entrpKey = GlobalKey();
final _prcsKey = GlobalKey();
final _locKey = GlobalKey();
final _ftrKey = GlobalKey();

EdgeInsets homeLargeInsects(Size size, {double? horizontal, double? vertical}) {
  return EdgeInsets.symmetric(
      horizontal: horizontal ?? size.width * .12,
      vertical: vertical ?? size.width * .04);
}

class WebViewWid extends StatelessWidget {
  const WebViewWid({super.key, this.widname});
  final String? widname;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    scroller();
    return SingleChildScrollView(
      child: Column(
        children: [
          Header(size: size), // 1
          ForEnterprise(key: _entrpKey, size: size), // 2
          Featured(key: _ftrKey, size: size), // 3
          OurProcess(key: _prcsKey, size: size), // 4
          ExamLocations(key: _locKey, size: size), // 5
          CertificationBox(size: size), // 6
          FooterBox(size: size), // 7
          CopyRightBar(size: size), // 8
        ],
      ),
    );
  }

  void scroller() {
    return WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      homeScroll(widname);
    });
  }
}

homeScroll(String? widName) {
  try {
    if (widName == null) return;
    switch (widName) {
      case "entr":
        if (_entrpKey.currentContext == null ||
            !(_entrpKey.currentState?.mounted ?? true)) return;
        Scrollable.ensureVisible(_entrpKey.currentContext!,
            duration: const Duration(milliseconds: 1000));
        break;
      case "prc":
        if (_prcsKey.currentContext == null ||
            !(_prcsKey.currentState?.mounted ?? true)) return;
        Scrollable.ensureVisible(_prcsKey.currentContext!,
            duration: const Duration(milliseconds: 1000));
        break;
      case "ftr":
        if (_ftrKey.currentContext == null ||
            !(_ftrKey.currentState?.mounted ?? true)) return;
        Scrollable.ensureVisible(_ftrKey.currentContext!,
            duration: const Duration(milliseconds: 1000));
        break;
      case "loc":
        if (_locKey.currentContext == null ||
            !(_locKey.currentState?.mounted ?? true)) return;
        Scrollable.ensureVisible(_locKey.currentContext!,
            duration: const Duration(milliseconds: 1000));
        break;
      default:
    }
  } catch (e) {
    debugPrint(e.toString());
  }
}

class WebViewWidOld extends StatelessWidget {
  const WebViewWidOld({super.key, this.yAxis});
  final int? yAxis;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return GetBuilder<HomeController>(
      init: Get.find<HomeController>(),
      builder: (_) {
        return WebViewX(
          initialContent: 'https://digitalcruze.com',
          // initialContent: 'https://flutter.dev/',
          javascriptMode: JavascriptMode.unrestricted,
          initialSourceType: SourceType.urlBypass,
          onWebViewCreated: (controller) async {
            _.webviewController = controller;
            if (yAxis == null) return;
            await Future.delayed(const Duration(milliseconds: 10));
            controller.scrollTo(0, yAxis!);
          },
          height: size.height,
          width: size.width,
          webSpecificParams: const WebSpecificParams(),
          onWebResourceError: (error) {
            debugPrint(error.toString());
          },
        );
      },
    );
  }
}
