import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

import '../../controllers/home_ctrl.dart';
import '../../models/cart_model.dart';
import '../../models/checkout_model.dart';
import '../../utils/consts.dart';
import '../../utils/methods.dart';
import '../../utils/router.dart';
import '../../utils/theme.dart';
import 'webview.dart';

class Featured extends StatelessWidget {
  const Featured({super.key, required this.size});
  final Size size;

  @override
  Widget build(BuildContext context) {
    bool large = size.width > breakPointLarge;
    bool small = size.width < breakPointMid;
    return Container(
      color: Theme.of(context).primaryColor.withOpacity(.1),
      padding: !large
          ? EdgeInsets.symmetric(
              horizontal: small ? 28 : size.width * .1,
              vertical: small ? 34 : size.width * .06)
          : homeLargeInsects(size),
      child: Column(
        children: [
          _topTextOne(),
          _topTextTwo(),
          const SizedBox(height: 40),
          !small
              ? Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          _image(true),
                        ],
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _textOne(),
                          const SizedBox(height: 18),
                          _textTwo(),
                          const SizedBox(height: 30),
                          _pointsColumn(),
                          const SizedBox(height: 32),
                          _button(context),
                        ],
                      ),
                    ),
                  ],
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(alignment: Alignment.center, child: _image(false)),
                    const SizedBox(height: 28),
                    _textOne(),
                    const SizedBox(height: 18),
                    _textTwo(),
                    const SizedBox(height: 30),
                    _pointsColumn(),
                    const SizedBox(height: 32),
                    _button(context),
                  ],
                ),
        ],
      ),
    );
  }

  ElevatedButton _button(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: Theme.of(context).primaryColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(4))),
      onPressed: () {
        if (!isLoggedIn()) {
          context.go(Routes.login);
          return;
        }
        context.go(Routes.checkout,
            extra: CheckoutModel(checkoutCourses: [
              CartCourseModel(courseId: currentCourseDocId)
            ], enableEdit: true));
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16),
        child: GetBuilder<HomeController>(
          init: Get.find<HomeController>(),
          builder: (_) {
            return Text(
              "Enroll Now - \$${_.courseList.firstWhereOrNull((element) => element.docId == currentCourseDocId)?.price ?? ""}",
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            );
          },
        ),
      ),
    );
  }

  Column _pointsColumn() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Buy in Bulk",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Assign Courses",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Easy to follow instructions",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Multiple Location",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Track Progress",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Have Access to Your Entire Organization's Certificates with a Single Login",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.check_mark_circled_solid,
              color: Colors.green,
            ),
            SizedBox(width: 20),
            Expanded(
              child: Text(
                "Extra Discounts Available on Volume",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Roboto',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Text _textTwo() {
    return Text(
      "Well Fed allows you to experience the highest standards of training, to keep your customers safe with advanced Food Safety knowledge. This certified Food Manager course is specially designed for foodservice and hospitality personnel.",
      style: appTextStyleOne.copyWith(
        fontSize: 16,
        color: Colors.grey.shade700,
        fontWeight: FontWeight.bold,
        // fontFamily: 'Roboto',
      ),
    );
  }

  Text _textOne() {
    return Text(
      "Experience advanced training at the comfort of your home",
      style: appTextStyleTwo.copyWith(
        fontSize: 24,
        height: 1.2,
        fontWeight: FontWeight.w900,
        // fontFamily: 'Roboto',
      ),
    );
  }

  Widget _image(bool inRow) {
    return SizedBox(
      height: inRow ? size.width * .30 : size.width * .7,
      width: inRow ? size.width * .30 : size.width * .7,
      child: Stack(
        alignment: Alignment.bottomRight,
        children: [
          Image.asset(
            'assets/4.webp',
            height: inRow ? size.width * .30 : size.width * .7,
            width: inRow ? size.width * .30 : size.width * .7,
            fit: BoxFit.cover,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    color: Colors.white,
                    child: Container(
                      color: appColorTwo,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 20.0),
                      child: const Center(
                        child: Text.rich(
                          TextSpan(
                              text: "15",
                              style: TextStyle(
                                  height: 1,
                                  fontSize: 50,
                                  color: Colors.white,
                                  fontFamily: 'Roboto'),
                              children: [
                                TextSpan(
                                  text: "+",
                                  style: TextStyle(
                                      fontSize: 12, color: Colors.white),
                                ),
                                TextSpan(
                                  text: "\nYears Expirence",
                                  style: TextStyle(
                                      fontSize: 12, color: Colors.white),
                                ),
                              ]),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }

  Text _topTextTwo() {
    return Text.rich(
      TextSpan(
          text: "Online ",
          style: appTextStyleTwo.copyWith(
            fontSize: 34,
            fontWeight: FontWeight.bold,
            // fontFamily: 'Roboto',
          ),
          children: [
            TextSpan(
              text: "Coaching ",
              style: appTextStyleTwo.copyWith(
                  fontSize: 34,
                  color: appColorTwo,
                  fontWeight: FontWeight.normal),
            ),
            TextSpan(
              text: "For Remote Learning",
              style: appTextStyleTwo.copyWith(
                fontSize: 34,
                fontWeight: FontWeight.bold,
                // fontFamily: 'Roboto',
              ),
            ),
          ]),
      textAlign: TextAlign.center,
    );
  }

  Text _topTextOne() {
    return const Text(
      "EDUCATION FOR EVERYONE",
      style: TextStyle(
          letterSpacing: 2,
          wordSpacing: 1.5,
          color: Colors.black54,
          fontSize: 15,
          fontFamily: 'Roboto',
          fontWeight: FontWeight.bold),
    );
  }
}
