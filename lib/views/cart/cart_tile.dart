import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import '../../models/course_model.dart';
import '../../utils/other.dart';

class CartTile extends StatelessWidget {
  const CartTile({
    super.key,
    required this.course,
    required this.qty,
    required this.isEnterprise,
  });

  final CourseModel course;
  final int qty;
  final bool isEnterprise;

  @override
  Widget build(BuildContext context) {
    return Card(
      surfaceTintColor: Colors.white,
      child: Container(
        padding: const EdgeInsets.all(8),
        constraints: BoxConstraints(maxHeight: isEnterprise ? 128 : 120),
        // decoration: BoxDecoration(
        //     border: Border.all(color: Colors.grey.shade200),
        //     color: Colors.white),
        child: Row(
          children: [
            Expanded(child: _ImageBox(imageUrl: course.imageUrl)),
            const SizedBox(width: 12),
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    course.title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 18),
                  ),
                  Text(
                    course.desc,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: Colors.grey.shade900),
                  ),
                  Text(
                    'By ${course.author}',
                    style: TextStyle(color: Colors.grey.shade700),
                  ),
                  if (isEnterprise)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6),
                      decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(450)),
                      child: Text(
                        "\$${course.bulkPrice} only for qty. ${course.bulkMinQty - 1}+",
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  const Spacer(),
                  Row(
                    children: [
                      const Icon(CupertinoIcons.timer, size: 18),
                      const SizedBox(width: 2),
                      Text.rich(
                        TextSpan(
                          text: '${course.duration.hours} ',
                          style: TextStyle(color: Colors.grey.shade900),
                          children: [
                            TextSpan(
                              text: "Hours ",
                              style: TextStyle(color: Colors.grey.shade700),
                            ),
                            TextSpan(
                              text: '${course.duration.minutes} ',
                              style: TextStyle(color: Colors.grey.shade900),
                            ),
                            TextSpan(
                              text: "Minutes",
                              style: TextStyle(color: Colors.grey.shade700),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 12.0),
                  child: Text(
                    qty >= course.bulkMinQty
                        ? '\$${course.bulkPrice}'
                        : '\$${course.price}',
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 18),
                  ),
                ),
                if (isEnterprise)
                  Row(
                    children: [
                      IconButton(
                          onPressed: () {
                            Get.find<HomeController>()
                                .subtractQtyToCourse(course.docId);
                          },
                          icon: const Icon(CupertinoIcons.minus_square)),
                      Text('$qty'),
                      IconButton(
                          onPressed: () {
                            Get.find<HomeController>()
                                .addQtyToCourse(course.docId);
                          },
                          icon: const Icon(CupertinoIcons.plus_app)),
                    ],
                  ),
                TextButton(
                  onPressed: () =>
                      Get.find<HomeController>().removeFromCart(course.docId),
                  child: const Text("Remove"),
                  // icon: const Icon(CupertinoIcons.xmark_square),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _ImageBox extends StatelessWidget {
  const _ImageBox({
    required this.imageUrl,
  });

  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: FadeInImage.memoryNetwork(
          placeholderErrorBuilder: (context, error, stackTrace) =>
              Image.memory(placeholderGrad),
          placeholder: placeholderGrad,
          fit: BoxFit.cover,
          image: imageUrl),
    );
  }
}
