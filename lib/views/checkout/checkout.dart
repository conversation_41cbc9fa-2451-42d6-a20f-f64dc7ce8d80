import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/checkout_model.dart';
import 'package:wellfed/models/course_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/other.dart';
import 'package:wellfed/utils/responsive.dart';
import 'package:wellfed/utils/router.dart';
import 'package:wellfed/utils/theme.dart';
import '../../models/cart_model.dart';
import 'checkout_tile.dart';
import 'summary_box.dart';

class CheckoutPage extends StatelessWidget {
  const CheckoutPage({super.key, required this.checkoutModel});
  final CheckoutModel? checkoutModel;

  @override
  Widget build(BuildContext context) {
    return checkoutModel?.checkoutCourses.isEmpty ?? true
        ? const Center(
            child: Text("Nothing to checkout"),
          )
        : ResponsiveWid(
            mobile: CheckoutWids(small: true, checkoutModel: checkoutModel!),
            tablet: CheckoutWids(small: true, checkoutModel: checkoutModel!),
            desktop: CheckoutWids(small: false, checkoutModel: checkoutModel!),
          );
  }
}

class CheckoutWids extends StatefulWidget {
  const CheckoutWids(
      {super.key, required this.small, required this.checkoutModel});
  final bool small;
  final CheckoutModel checkoutModel;

  @override
  State<CheckoutWids> createState() => _CheckoutWidsState();
}

class _CheckoutWidsState extends State<CheckoutWids> {
  List<CartCourseModel> checkoutCourses = <CartCourseModel>[];
  bool loading = false;
  @override
  void initState() {
    super.initState();
    checkoutCourses.addAll(widget.checkoutModel.checkoutCourses);
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final sidePadding = size.width * .2;
    return checkoutCourses.isEmpty
        ? const Center(
            child: Text("Nothing to checkout"),
          )
        : GetBuilder<HomeController>(
            init: Get.find<HomeController>(),
            builder: (_) {
              double originalPrice = 0;
              double discount = 0;
              double total = 0;
              for (var element in checkoutCourses) {
                final crse = _.courseList
                    .firstWhereOrNull((e) => e.docId == element.courseId);
                if (crse != null) {
                  if (_.isEnterprise && element.qty >= crse.bulkMinQty) {
                    total += crse.bulkPrice * element.qty;
                    originalPrice += crse.discountPrice * element.qty;
                    discount += originalPrice - total;
                  } else {
                    total += crse.price * element.qty;
                    originalPrice += crse.discountPrice * element.qty;
                    discount += originalPrice - total;
                  }
                }
              }
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: widget.small
                        ? SingleChildScrollView(
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(20.0),
                                  child: _listOfWids(_.courseList,
                                      checkoutCourses, _.isEnterprise),
                                ),
                                const SizedBox(height: 20),
                                const Divider(),
                                SummaryBox(
                                  oroginalPrice: originalPrice,
                                  discount: discount,
                                  small: widget.small,
                                  loading: loading,
                                  total: total,
                                  onCheckout: () async {
                                    setState(() => loading = true);
                                    final orderId = await _.onCheckout(
                                        context,
                                        checkoutCourses,
                                        total,
                                        discount,
                                        originalPrice,
                                        !widget.checkoutModel.enableEdit);
                                    setState(() => loading = false);
                                    if (orderId != null) {
                                      showStatusDialog(orderId);
                                    }
                                  },
                                )
                              ],
                            ),
                          )
                        : Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                flex: 3,
                                child: Column(
                                  children: [
                                    Expanded(
                                      child: Container(
                                        color: Theme.of(context)
                                            .primaryColor
                                            .withOpacity(.05),
                                        padding: EdgeInsets.fromLTRB(
                                            sidePadding, 20, 20, 20),
                                        child: SingleChildScrollView(
                                            child: _listOfWids(
                                                _.courseList,
                                                checkoutCourses,
                                                _.isEnterprise)),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Container(
                                  color: Colors.white,
                                  padding:
                                      const EdgeInsets.fromLTRB(20, 20, 20, 20),
                                  child: Row(
                                    children: [
                                      SummaryBox(
                                        oroginalPrice: originalPrice,
                                        discount: discount,
                                        small: widget.small,
                                        loading: loading,
                                        total: total,
                                        onCheckout: () async {
                                          setState(() => loading = true);
                                          final orderResp = await _.onCheckout(
                                              context,
                                              checkoutCourses,
                                              total,
                                              discount,
                                              originalPrice,
                                              !widget.checkoutModel.enableEdit);
                                          setState(() => loading = false);
                                          if (orderResp != null) {
                                            _launchPayUrl(orderResp);
                                            showStatusDialog(orderResp);
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                  ),
                ],
              );
            },
          );
  }

  showStatusDialog(PayRespReturn orderResp) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  "Processing Your Order",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const Divider(),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: StreamBuilder(
                    stream:
                        FBFireStore.orders.doc(orderResp.orderId).snapshots(),
                    builder: (BuildContext context, snapshot) {
                      if (snapshot.hasError) {
                        debugPrint(snapshot.error.toString());
                        return const Text("Something went wrong!");
                      }
                      if (snapshot.hasData) {
                        if (snapshot.data == null) {
                          return const Text("No Order Found!");
                        }
                        final processed = snapshot.data!['processed'];
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            processed
                                ? const Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text("Your purchase was successful"),
                                      SizedBox(width: 8),
                                      Icon(
                                        CupertinoIcons.check_mark_circled,
                                        color: Colors.green,
                                      ),
                                    ],
                                  )
                                : Column(
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const Text("Waiting for payment"),
                                          const SizedBox(width: 8),
                                          loaderBeat(
                                              color: Theme.of(context)
                                                  .primaryColor),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      ElevatedButton(
                                          onPressed: () =>
                                              _launchPayUrl(orderResp),
                                          child: const Text("Go to Payment")),
                                    ],
                                  ),
                            const SizedBox(height: 16),
                            if (processed)
                              ElevatedButton(
                                  onPressed: () {
                                    context.pop();
                                    context.go(Routes.mycourses);
                                  },
                                  child: const Text("Go to My Courses")),
                            TextButton(
                                onPressed: () => context.pop(),
                                child: const Text("Close")),
                          ],
                        );
                      }
                      return loaderWave(color: Theme.of(context).primaryColor);
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _launchPayUrl(PayRespReturn resp) async {
    try {
//       import 'dart:html' as html;
// void htmlOpenLink() {
//  String url = 'https://flutter.dev';
//  html.window.open(url, '_blank');
// }
      final url = Uri.parse(resp.msg);
      if (await canLaunchUrl(url)) {
        await launchUrl(url,
            mode: LaunchMode.externalApplication,
            webOnlyWindowName: "WellFed",
            webViewConfiguration:
                const WebViewConfiguration(enableJavaScript: true));
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

/*   _onCheckout(double total, double discount, double original) async {
    Get.find<HomeController>().onCheckout(context, checkoutCourses, total,
        discount, original, !widget.checkoutModel.enableEdit);
  }
 */
  Widget _listOfWids(List<CourseModel> courseList,
      List<CartCourseModel> checkoutList, bool isEnterprise) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _TitleText(),
        const SizedBox(height: 20),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Order Details',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ListView.builder(
                padding: const EdgeInsets.all(2),
                itemCount: checkoutList.length,
                shrinkWrap: true,
                itemBuilder: (BuildContext context, int index) {
                  final course = courseList.firstWhereOrNull((element) =>
                      element.docId == checkoutList[index].courseId);
                  return course == null
                      ? const SizedBox()
                      : CheckoutTile(
                          course: course,
                          qty: checkoutList[index].qty,
                          isEnterprise: isEnterprise,
                          enableEdit: widget.checkoutModel.enableEdit,
                          incrementQty: incrementQty,
                        );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  incrementQty(String courseId, bool add) {
    add
        ? checkoutCourses
            .firstWhereOrNull((element) => element.courseId == courseId)
            ?.qty++
        : checkoutCourses
            .firstWhereOrNull((element) => element.courseId == courseId)
            ?.qty--;
    setState(() {});
    // Get.find<HomeController>().update();
  }
}

class _TitleText extends StatelessWidget {
  const _TitleText();

  @override
  Widget build(BuildContext context) {
    return Text(
      "Checkout",
      style:
          appTitleTextStyle.copyWith(fontSize: 32, fontWeight: FontWeight.bold),
    );
  }
}
