import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/router.dart';
import '../../utils/other.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailCtrl = TextEditingController();
  final passCtrl = TextEditingController();
  final loginErrorText = TextEditingController();
  bool loading = false;
  bool emailError = false;
  bool passError = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Center(
      child: Column(
        children: [
          const Spacer(flex: 3),
          Image.asset(
            'assets/logo.png',
            width: 240,
          ),
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(28),
            constraints: const BoxConstraints(maxWidth: 450),
            decoration: ShapeDecoration(
                color: Colors.blueGrey.shade50,
                shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadius.circular(24))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "Sign In",
                  style: TextStyle(fontSize: 32, fontWeight: FontWeight.w200),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.only(left: 4.0),
                  child: Text(
                    "EMAIL",
                    style: _textStyle(),
                  ),
                ),
                const SizedBox(height: 8),
                Material(
                  elevation: 2,
                  borderRadius: BorderRadius.circular(6),
                  child: TextField(
                    controller: emailCtrl,
                    keyboardType: TextInputType.emailAddress,
                    decoration: _decor().copyWith(
                        hintText: "Email Address",
                        prefixIcon: const Icon(CupertinoIcons.mail_solid)),
                  ),
                ),
                if (emailError)
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 6.0, vertical: 4),
                    child: Text(
                      "Enter a valid email",
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                const SizedBox(height: 20),
                Text(
                  "PASSWORD",
                  style: _textStyle(),
                ),
                const SizedBox(height: 8),
                Material(
                  elevation: 2,
                  borderRadius: BorderRadius.circular(6),
                  child: TextField(
                    controller: passCtrl,
                    obscureText: true,
                    keyboardType: TextInputType.visiblePassword,
                    onSubmitted: (value) => _onSignIn(),
                    decoration: _decor().copyWith(
                        hintText: "Enter Password",
                        prefixIcon:
                            const Icon(CupertinoIcons.lock_shield_fill)),
                  ),
                ),
                if (passError)
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 6.0, vertical: 4),
                    child: Text(
                      "Enter atleast 6 characters",
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                        onTap: _forgotPass,
                        child: Text(
                          "Forgot Password?",
                          style:
                              TextStyle(color: Theme.of(context).primaryColor),
                        ))
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              shape: ContinuousRectangleBorder(
                                  borderRadius: BorderRadius.circular(24)),
                              minimumSize: const Size.fromHeight(52)),
                          onPressed: _onSignIn,
                          child: loading
                              ? loaderWave()
                              : const Text(
                                  "SIGN IN",
                                  style: TextStyle(color: Colors.white),
                                )),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Row(
                    children: [
                      const Text(
                        "Don't have an account? ",
                        style: TextStyle(),
                      ),
                      InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => context.go(Routes.register),
                        child: Text(
                          "Register",
                          style:
                              TextStyle(color: Theme.of(context).primaryColor),
                        ),
                      )
                    ],
                  ),
                ),
                if (loginErrorText.text.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 6.0, vertical: 4),
                    child: Text(
                      loginErrorText.text,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
              ],
            ),
          ),
          const Spacer(flex: 4),
          const DevelopedByDefault(),
          const SizedBox(height: 4),
        ],
      ),
    ));
  }

  void _forgotPass() async {
    try {
      if (!emailCtrl.text.isEmail) {
        if (context.mounted) {
          showAppSnackBar(context, "Sorry", "Please, enter a valid email!");
        }
        return;
      }
      setState(() => loading = true);
      await FBAuth.auth.sendPasswordResetEmail(email: emailCtrl.text);
      setState(() => loading = false);
      if (context.mounted) {
        showAppSnackBar(context, "Done", "Password reset email sent!");
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void _onSignIn() async {
    try {
      if (loading) return;
      if (_validate()) {
        setState(() => loading = true);
        await FirebaseAuth.instance.signInWithEmailAndPassword(
            email: emailCtrl.text, password: passCtrl.text);
        setState(() => loading = false);
        loginErrorText.clear();
        if (context.mounted) context.go(Routes.home);
      }
    } on FirebaseAuthException catch (e) {
      loginErrorText.text = _getErrorMessage(e.message ?? "");
      setState(() => loading = false);
      debugPrint(e.toString());
    }
  }

  bool _validate() {
    try {
      bool validate = true;
      if (!emailCtrl.text.isEmail) {
        validate = false;
        emailError = true;
      } else {
        emailError = false;
      }
      if (passCtrl.text.length < 6) {
        validate = false;
        passError = true;
      } else {
        passError = false;
      }
      setState(() {});
      return validate;
    } catch (e) {
      return false;
    }
  }

  String _getErrorMessage(String msg) {
    if (msg.contains("user-not-found")) {
      return "No user found with provided email.";
    } else if (msg.contains("wrong-password")) {
      return "Incorrect Password!";
    } else {
      return "Something went wrong!";
    }
  }

  InputDecoration _decor() {
    return InputDecoration(
      filled: true,
      fillColor: Colors.white,
      enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6), borderSide: BorderSide.none),
      errorBorder: InputBorder.none,
    );
  }

  TextStyle _textStyle() {
    return TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 12,
        color: Colors.blueGrey.shade700);
  }
}
