import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/utils/consts.dart';
import 'package:wellfed/utils/responsive.dart';
import 'package:wellfed/utils/theme.dart';
import '../../../controllers/home_ctrl.dart';
import 'courses_box.dart';
import 'overview_box.dart';

class MyDashBoard extends StatelessWidget {
  const MyDashBoard({super.key});

  @override
  Widget build(BuildContext context) {
    return const ResponsiveWid(
      mobile: DashboardWids(small: true),
      tablet: DashboardWids(small: false),
      desktop: DashboardWids(small: false),
    );
  }
}

class DashboardWids extends StatefulWidget {
  const DashboardWids({super.key, required this.small});
  final bool small;

  @override
  State<DashboardWids> createState() => _DashboardWidsState();
}

class _DashboardWidsState extends State<DashboardWids> {
  final myCourseSearch = TextEditingController();
  @override
  Widget build(BuildContext context) {
    // print(MediaQuery.sizeOf(context).width);
    return GetBuilder<HomeController>(
      init: Get.find<HomeController>(),
      builder: (_) {
        List<DashboardCourse> myCourses = <DashboardCourse>[];
        List<DashboardCourse> filteredCourses = <DashboardCourse>[];
        for (var element in _.myCourses) {
          myCourses.add(DashboardCourse(
              _.courseList.firstWhereOrNull((e) => e.docId == element.courseId),
              element));
        }
        filteredCourses = myCourses
            .where((element) =>
                element.course?.title
                    .toLowerCase()
                    .contains(myCourseSearch.text.toLowerCase()) ??
                false)
            .toList();
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: ResponsiveCustomBuilder(
            smallSize: mobileMinSize2,
            largeSize: 1200,
            mobileBuilder: (width) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    _mobile(myCourses, filteredCourses, _.isEnterprise),
                  ],
                ),
              );
            },
            tabletBuilder: (width) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    if (width > mobileMinSize3)
                      const Padding(
                          padding: EdgeInsets.only(left: 20), child: Header()),
                    if (width > mobileMinSize3) const SizedBox(height: 30),
                    _tablet(myCourses, filteredCourses, _.isEnterprise),
                  ],
                ),
              );
            },
            desktopBuilder: (width) {
              return Column(
                children: [
                  const Header(),
                  const SizedBox(height: 30),
                  Expanded(
                      child:
                          _desktop(myCourses, filteredCourses, _.isEnterprise)),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Widget _mobile(List<DashboardCourse> myCourses,
      List<DashboardCourse> filteredCourses, bool isEnterprise) {
    return Column(
      children: [
        CoursesContainer(
          myCourseSearch: myCourseSearch,
          filteredCourses: filteredCourses,
          expanded: false,
          gridCount: 1,
        ),
        OverviewBox(
          corses: myCourses,
          limited: true,
          vertical: true,
          isEnterprise: isEnterprise,
        ),
      ],
    );
  }

  Widget _tablet(List<DashboardCourse> myCourses,
      List<DashboardCourse> filteredCourses, bool isEnterprise) {
    return Column(
      children: [
        OverviewBox(
          corses: myCourses,
          limited: true,
          vertical: false,
          isEnterprise: isEnterprise,
        ),
        CoursesContainer(
          myCourseSearch: myCourseSearch,
          filteredCourses: filteredCourses,
          expanded: false,
          gridCount: 2,
        )
      ],
    );
  }

  Widget _desktop(List<DashboardCourse> myCourses,
      List<DashboardCourse> filteredCourses, bool isEnterprise) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: CoursesContainer(
              myCourseSearch: myCourseSearch,
              filteredCourses: filteredCourses,
              expanded: true,
              gridCount: 2),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OverviewBox(
            corses: myCourses,
            limited: false,
            vertical: true,
            isEnterprise: isEnterprise,
          ),
        ),
      ],
    );
  }
}

class Header extends StatelessWidget {
  const Header({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: 30,
          width: 5,
          decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(.7),
              borderRadius: BorderRadius.circular(2)),
        ),
        const SizedBox(width: 8),
        Text(
          "Dashboard",
          style: appTextStyleOne.copyWith(
              fontSize: 20, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
}
