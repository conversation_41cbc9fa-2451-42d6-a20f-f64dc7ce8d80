import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/router.dart';

class DashboardDrawer extends StatelessWidget {
  const DashboardDrawer({
    super.key,
    required this.isEnterprise,
  });
  final bool isEnterprise;
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: DashDrawerWids(
        isEnterprise: isEnterprise,
      ),
    );
  }
}

class DashDrawerWids extends StatelessWidget {
  const DashDrawerWids({super.key, required this.isEnterprise});

  final bool isEnterprise;

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 20,
      child: Container(
        color: Theme.of(context).primaryColor.withOpacity(.05),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _logo(context),
            _buttons(context),
          ],
        ),
      ),
    );
  }

  Widget _buttons(BuildContext context) {
    final ctrl = Get.find<HomeController>();
    return Column(
      children: ctrl.isEnterprise
          ? [
              _button(
                  context, Routes.mycourses, "Dashboard", CupertinoIcons.home),
              // _button(context, Routes.users, "Users",
              //     CupertinoIcons.person_2_square_stack),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
                child: ExpansionTile(
                  leading: const Icon(
                      CupertinoIcons.list_bullet_below_rectangle,
                      size: 24),
                  clipBehavior: Clip.hardEdge,
                  collapsedShape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                  title: const Text(
                    "Branches",
                    style: TextStyle(fontSize: 15),
                  ),
                  childrenPadding: EdgeInsets.zero,
                  children: [
                    ListView.builder(
                      itemCount: ctrl.branches.length,
                      shrinkWrap: true,
                      itemBuilder: (BuildContext context, int index) {
                        return _button(
                            context,
                            '${Routes.users}/${ctrl.branches[index].bId}',
                            ctrl.branches[index].name,
                            null);
                      },
                    ),
                  ],
                ),
              ),
              _addBranchButton(context),
            ]
          : [
              _button(context, Routes.home, "Home", CupertinoIcons.home),
              _button(context, Routes.mycourses, "Dashboard",
                  CupertinoIcons.chart_bar_square),
              _button(context, Routes.exams, "Exam Schedules",
                  CupertinoIcons.calendar_today),
              _button(context, Routes.certificates, "Certificates",
                  CupertinoIcons.doc_text),
              // _button(context, Routes.offline, "QR (Offline)",
              //     CupertinoIcons.qrcode),
            ],
    );
  }

  AnimatedContainer _button(
      BuildContext context, String routeName, String name, IconData? icon) {
    final isSelected =
        appRouter.routeInformationProvider.value.location == routeName;
    return AnimatedContainer(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      duration: const Duration(milliseconds: 250),
      decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withOpacity(.1)
              : null,
          borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          myCoursesScafKey.currentState?.closeDrawer();
          context.go(routeName);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          child: Row(
            children: [
              if (icon != null)
                Icon(icon,
                    color: isSelected
                        ? Theme.of(context).primaryColor.withOpacity(.8)
                        : null),
              const SizedBox(width: 8),
              Text(
                name,
                style: TextStyle(
                    color: isSelected ? Theme.of(context).primaryColor : null),
              ),
              if (icon == null) const Spacer(),
              if (icon == null) const CupertinoListTileChevron()
            ],
          ),
        ),
      ),
    );
  }

  AnimatedContainer _addBranchButton(BuildContext context) {
    return AnimatedContainer(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      duration: const Duration(milliseconds: 250),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => addBranchPopup(context),
        child: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          child: Row(
            children: [
              Icon(CupertinoIcons.add),
              SizedBox(width: 8),
              Text(
                "Add Branch",
                style: TextStyle(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  addBranchPopup(BuildContext context) {
    final nameCtrl = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Add New Branch"),
        content: TextField(
          controller: nameCtrl,
        ),
        actionsAlignment: MainAxisAlignment.center,
        actions: [
          ElevatedButton(
            onPressed: () async {
              try {
                if (context.mounted) context.pop();
                await FBFireStore.eBranches.add({
                  "name": nameCtrl.text,
                  "bId": getRandomId(12),
                  "uId": FBAuth.auth.currentUser?.uid,
                  "time": FieldValue.serverTimestamp(),
                });
              } catch (e) {
                debugPrint(e.toString());
              }
            },
            child: const Text("Confirm"),
          ),
        ],
      ),
    );
  }

  Widget _logo(BuildContext context) => InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () => context.go(Routes.home),
        child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
            child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 200),
                child: Image.asset('assets/logo.png'))),
      );
}
