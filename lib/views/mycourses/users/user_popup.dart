import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/dashboard_course.dart';
import 'package:wellfed/models/other_certi_model.dart';
import 'package:wellfed/models/user_course_model.dart';
import 'package:wellfed/models/user_model.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/views/mycourses/users/assign_crse_popup.dart';

import '../../../utils/firebase.dart';
import '../../../utils/loaders.dart';
import '../../../utils/router.dart';
import '../dashboard/courses_box.dart';
import 'add_certi_pop.dart';

class UserDetailPopup extends StatefulWidget {
  const UserDetailPopup({super.key, required this.user});
  final UserModel user;

  @override
  State<UserDetailPopup> createState() => _UserDetailPopupState();
}

class _UserDetailPopupState extends State<UserDetailPopup>
    with SingleTickerProviderStateMixin {
  late TabController tabCtrl;
  @override
  void initState() {
    super.initState();
    tabCtrl = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.user.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(widget.user.email),
                    Text(widget.user.contact),
                    // Text('${widget.user.contact}, ${widget.user.branch}'),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: tabCtrl.index == 0
                        ? null
                        : Theme.of(context).primaryColor),
                onPressed: () => tabCtrl.index == 0
                    ? _assignCoursePop(widget.user)
                    : _addCertiPop(widget.user),
                child: Text(
                  tabCtrl.index == 0 ? "Assign Course" : "Add Certi",
                  style: TextStyle(
                      color: tabCtrl.index == 0 ? null : Colors.white),
                ),
              ),
            ],
          ),
          const Divider(),
          TabBar(
            controller: tabCtrl,
            isScrollable: true,
            tabs: const [
              Padding(
                padding: EdgeInsets.all(8.0),
                child: Text("Courses"),
              ),
              Padding(
                padding: EdgeInsets.all(8.0),
                child: Text("Other Certi"),
              ),
            ],
            onTap: (value) => setState(() {}),
          ),
          tabCtrl.index == 0
              ? StreamBuilder<List<UserCourseModel>>(
                  stream: FBFireStore.userCourses
                      // .where('assignedByUid',
                      //     isEqualTo: FBAuth.auth.currentUser!.uid)
                      .where('uid', isEqualTo: widget.user.docId)
                      .snapshots()
                      .map((event) => event.docs
                          .map((e) => UserCourseModel.fromJson(e.id, e.data()))
                          .toList()),
                  builder: (context, snapshot) {
                    if (snapshot.hasError) {
                      debugPrint(snapshot.error.toString());
                      return const Center(
                        child: Text("Something went wrong!"),
                      );
                    }
                    if (snapshot.hasData) {
                      return EUserCoursesWids(crseList: snapshot.data ?? []);
                    }
                    return Center(
                        child:
                            loaderWave(color: Theme.of(context).primaryColor));
                  })
              : StreamBuilder<List<OtherCertiModel>>(
                  stream: FBFireStore.otherCerti
                      // .where('assignedByUid',
                      //     isEqualTo: FBAuth.auth.currentUser!.uid)
                      .where('uid', isEqualTo: widget.user.docId)
                      .snapshots()
                      .map((event) =>
                          OtherCertiModel.toCertiList(event).toList()),
                  builder: (context, snapshot) {
                    if (snapshot.hasError) {
                      debugPrint(snapshot.error.toString());
                      return const Center(
                        child: Text("Something went wrong!"),
                      );
                    }
                    if (snapshot.hasData) {
                      return EUserCertiWid(certiList: snapshot.data ?? []);
                    }
                    return Center(
                        child:
                            loaderWave(color: Theme.of(context).primaryColor));
                  })
        ],
      ),
    );
  }

  _assignCoursePop(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(child: AssignCoursePopup(user: user));
      },
    );
  }

  _addCertiPop(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(child: AddCertiPopup(user: user));
      },
    );
  }
}

class EUserCoursesWids extends StatefulWidget {
  const EUserCoursesWids({super.key, required this.crseList});
  final List<UserCourseModel> crseList;
  @override
  State<EUserCoursesWids> createState() => _EUserCoursesWidsState();
}

class _EUserCoursesWidsState extends State<EUserCoursesWids> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        widget.crseList.isEmpty
            ? const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Text("No course assigned yet!"),
                ),
              )
            : SingleChildScrollView(
                // padding: const EdgeInsets.all(12),
                child: ListView.builder(
                  padding: const EdgeInsets.only(top: 12),
                  itemCount: widget.crseList.length,
                  shrinkWrap: true,
                  itemBuilder: (BuildContext context, int index) {
                    final uCrse = widget.crseList[index];
                    final crse = Get.find<HomeController>()
                        .courseList
                        .firstWhereOrNull((element) =>
                            element.docId == widget.crseList[index].courseId);
                    final expiresIn = uCrse.endDate!
                        .toDate()
                        .difference(DateTime.now())
                        .inDays;
                    final percentage =
                        getPercentage(DashboardCourse(crse, uCrse));
                    return Card(
                      clipBehavior: Clip.hardEdge,
                      child: ExpansionTile(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6)),
                        title: Text(crse?.title ?? ""),
                        subtitle: Text.rich(
                          TextSpan(text: "Status: ", children: [
                            TextSpan(
                              text: uCrse.completed
                                  ? " Completed "
                                  : expiresIn >= 0
                                      ? "Expires in $expiresIn days"
                                      : " Expired ",
                              style: TextStyle(
                                  color: uCrse.completed
                                      ? Colors.green
                                      : Colors.red),
                            )
                          ]),
                        ),
                        trailing: uCrse.completed
                            ? IconButton(
                                tooltip: uCrse.certiUrl == null
                                    ? "Waiting for Upload"
                                    : "View Certi",
                                onPressed: () => uCrse.certiUrl != null
                                    ? context
                                        .go('${Routes.certi}/${uCrse.certiUrl}')
                                    : null,
                                icon: uCrse.certiUrl == null
                                    ? const Icon(CupertinoIcons.hourglass)
                                    : const Icon(CupertinoIcons.doc_append))
                            : CircularPercentIndicator(
                                radius: 20.0,
                                lineWidth: 5.0,
                                percent: percentage,
                                // percent: 0.60,
                                // restartAnimation: true,
                                animationDuration: 1000,
                                animation: true,
                                center: Text(
                                  "${percentage * 100}%",
                                  style: const TextStyle(fontSize: 12),
                                ),
                                progressColor: Theme.of(context).primaryColor,
                              ),
                        expandedCrossAxisAlignment: CrossAxisAlignment.start,
                        expandedAlignment: Alignment.centerLeft,
                        childrenPadding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        children: [
                          Text.rich(
                            TextSpan(
                                text: "Exam Type: ",
                                style: TextStyle(color: Colors.grey.shade700),
                                children: [
                                  TextSpan(
                                    text: crse?.examType,
                                    style: const TextStyle(color: Colors.black),
                                  )
                                ]),
                          ),
                          Text.rich(
                            TextSpan(
                                text: "Assigned On: ",
                                style: TextStyle(color: Colors.grey.shade700),
                                children: [
                                  TextSpan(
                                    text:
                                        uCrse.startDate?.toDate().goodDayDate(),
                                    style: const TextStyle(color: Colors.black),
                                  )
                                ]),
                          ),
                          if (uCrse.completed)
                            Text.rich(
                              TextSpan(
                                  text: "Completed On: ",
                                  style: TextStyle(color: Colors.grey.shade700),
                                  children: [
                                    TextSpan(
                                      text: uCrse.completedOn
                                          ?.toDate()
                                          .goodDayDate(),
                                      style:
                                          const TextStyle(color: Colors.black),
                                    )
                                  ]),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),
      ],
    );
  }
}

class EUserCertiWid extends StatefulWidget {
  const EUserCertiWid({super.key, required this.certiList});
  final List<OtherCertiModel> certiList;
  @override
  State<EUserCertiWid> createState() => _EUserCertiWidState();
}

class _EUserCertiWidState extends State<EUserCertiWid> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        widget.certiList.isEmpty
            ? const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Text("No certifiate added yet!"),
                ),
              )
            : SingleChildScrollView(
                // padding: const EdgeInsets.all(12),
                child: ListView.builder(
                  padding: const EdgeInsets.only(top: 12),
                  itemCount: widget.certiList.length,
                  shrinkWrap: true,
                  itemBuilder: (BuildContext context, int index) {
                    final certi = widget.certiList[index];
                    return Card(
                      clipBehavior: Clip.hardEdge,
                      child: ListTile(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6)),
                        title: Text(certi.title),
                        subtitle: Text.rich(
                          TextSpan(
                              text: "Added On: ",
                              style: TextStyle(color: Colors.grey.shade700),
                              children: [
                                TextSpan(
                                  text: certi.addedOn.goodDayDate(),
                                  style: const TextStyle(color: Colors.black),
                                )
                              ]),
                        ),
                        trailing: IconButton(
                            onPressed: () async {
                              try {
                                final url = Uri.parse(certi.url);
                                if (await canLaunchUrl(url)) {
                                  launchUrl(url,
                                      mode: LaunchMode.externalApplication);
                                }
                              } catch (e) {
                                debugPrint(e.toString());
                              }
                            },
                            icon: const Icon(CupertinoIcons.cloud_download)),
                      ),
                    );
                  },
                ),
              ),
      ],
    );
  }
}
