import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/e_branch_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/utils/methods.dart';
import 'package:wellfed/utils/theme.dart';

class AddUserDialog extends StatefulWidget {
  const AddUserDialog(
      {super.key, required this.userImportNoti, required this.bId});
  final Function userImportNoti;
  final String? bId;

  @override
  State<AddUserDialog> createState() => _AddUserDialogState();
}

class _AddUserDialogState extends State<AddUserDialog> {
  final formKey = GlobalKey<FormState>();
  final nameCtrl = TextEditingController();
  final emailCtrl = TextEditingController();
  final phoneCtrl = TextEditingController();
  // final branchCtrl = TextEditingController();
  final errorCtrl = TextEditingController();
  final branches = Get.find<HomeController>().branches;
  EBranch? selectedBranch;
  PhoneNumber? phoneNumber = PhoneNumber(isoCode: "IN", dialCode: "+91");
  bool loading = false;

  @override
  void initState() {
    super.initState();
    selectedBranch =
        branches.firstWhereOrNull((element) => element.bId == widget.bId);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                "Create New User",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const Divider(height: 28),
              Form(
                key: formKey,
                child: Column(
                  children: [
                    TextFormField(
                      controller: nameCtrl,
                      validator: (value) => value!.isEmpty ? "Required" : null,
                      decoration:
                          _inputDecor().copyWith(labelText: "Full Name"),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: emailCtrl,
                      validator: (value) => value!.isEmpty || !value.isEmail
                          ? "Require a valid email"
                          : null,
                      decoration:
                          _inputDecor().copyWith(labelText: "Email Address"),
                    ),
                    const SizedBox(height: 16),
                    InternationalPhoneNumberInput(
                      initialValue: phoneNumber,
                      selectorConfig: const SelectorConfig(
                          leadingPadding: 12,
                          trailingSpace: false,
                          setSelectorButtonAsPrefixIcon: true,
                          selectorType: PhoneInputSelectorType.DIALOG),
                      keyboardType:
                          const TextInputType.numberWithOptions(signed: true),
                      inputDecoration:
                          _inputDecor().copyWith(hintText: "Contact Number"),
                      validator: (p0) => null,
                      // validator: (p0) => p0!.isPhoneNumber
                      //     ? null
                      //     : "Enter a valid phone number",
                      onInputChanged: (value) => phoneNumber = value,
                      textFieldController: phoneCtrl,
                    ),
                    // const SizedBox(height: 12),
                    // TextFormField(
                    //   controller: branchCtrl,
                    //   decoration:
                    //       _inputDecor().copyWith(labelText: "Branch/ Location"),
                    // ),
                    const SizedBox(height: 16),
                    _branchDropdown(),
                    const SizedBox(height: 16),
                    const Text(
                      "Note: Provided Fullname will be used for cetification.",
                      style: TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).primaryColor,
                                minimumSize: const Size.fromHeight(50),
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8))),
                            onPressed: _onSubmit,
                            child: loading
                                ? loaderWave()
                                : const Text(
                                    "Confirm",
                                    style: TextStyle(color: Colors.white),
                                  ),
                          ),
                        ),
                      ],
                    ),
                    if (emailCtrl.text.isNotEmpty) const SizedBox(height: 12),
                    if (emailCtrl.text.isNotEmpty)
                      Text(
                        errorCtrl.text,
                        style: const TextStyle(color: Colors.red),
                      ),
                    const SizedBox(height: 12),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  DropdownSearch<EBranch> _branchDropdown() {
    return DropdownSearch<EBranch>(
      itemAsString: (EBranch i) => i.name,
      items: List.generate(branches.length, (index) => branches[index]),
      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration:
            _inputDecor().copyWith(labelText: "Branch/ Location"),
      ),
      dropdownButtonProps: const DropdownButtonProps(),
      onChanged: (value) {
        setState(() {
          // branchCtrl.text = value?.name ?? "";
          selectedBranch = value;
        });
      },
      selectedItem: selectedBranch,
      popupProps: PopupProps.menu(
          itemBuilder: (context, item, isSelected) => Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20),
                child: Text(
                  item.name,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
          searchDelay: const Duration(seconds: 0),
          showSelectedItems: true,
          searchFieldProps: TextFieldProps(
              decoration: _inputDecor().copyWith(
                  labelText: "Search",
                  enabledBorder: const OutlineInputBorder(
                      borderSide: BorderSide(color: appColorOne)))),
          showSearchBox: true),
      compareFn: (item1, item2) => item1.bId == item2.bId,
    );
  }

  _onSubmit() async {
    try {
      if (loading) return;
      if (formKey.currentState!.validate()) {
        errorCtrl.clear();
        setState(() => loading = true);
        final result = await FBFunctions.ff.httpsCallable('createUser').call(
          {
            "eUid": FBAuth.auth.currentUser?.uid,
            "name": nameCtrl.text,
            "email": emailCtrl.text,
            "phone": '${phoneNumber?.dialCode}${phoneCtrl.text}',
            "branch": selectedBranch?.name,
            "bId": selectedBranch?.bId,
            "eId": Get.find<HomeController>().userData?.eId,
          },
        ); // <EMAIL>
        final response = result.data;
        if (response['success'] == true) {
          if (context.mounted) {
            context.pop();
            showAppSnackBar(context, "Done", "User created successfully!");
          }
        } else {
          if (context.mounted) {
            showAppSnackBar(
                context, "Sorry", result.data['msg'], ContentType.failure);
          }
          if (response['code'] == "auth/email-already-exists") {
            if (context.mounted) {
              context.pop();
            }
            _importUserDialog();
          }
        }
        errorCtrl.text = result.data['msg'];
        setState(() => loading = false);
      }
    } catch (e) {
      debugPrint(e.toString());
      setState(() => loading = false);
    }
  }

  _importUserDialog() async {
    try {
      final res = await showGeneralDialog<bool>(
        context: context,
        pageBuilder: (context, animation, secondaryAnimation) => AlertDialog(
          title: const Text("User with provided email already exist!"),
          content: const Text("Want to import user data and certificates?"),
          actions: [
            TextButton(
              onPressed: () => context.pop(false),
              child: const Text("No"),
            ),
            TextButton(
              onPressed: () => context.pop(true),
              child: const Text("Sure"),
            ),
          ],
        ),
      );
      if (res == true) {
        // Importing user
        try {
          final userDoc = await FBFireStore.users
              .where("email", isEqualTo: emailCtrl.text)
              .get();
          if (userDoc.size > 0) {
            if (userDoc.docs.first['eId'] == null) {
              await FBFireStore.users.doc(userDoc.docs.first.id).update({
                "linkedWith.${Get.find<HomeController>().userData!.eId}": true,
                "bIds.${selectedBranch?.bId}": true,
              });
              widget.userImportNoti();
            } else {
              if (context.mounted) {
                showAppSnackBar(context, "Ops!",
                    "This email is used by an enterprise. Cannot be imported!");
              }
            }
          } else {
            if (context.mounted) {
              showAppSnackBar(context, "Ops!", "Something went wrong!");
            }
          }
        } catch (e) {
          debugPrint(e.toString());
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  InputDecoration _inputDecor() => InputDecoration(
        fillColor: Colors.grey.shade100,
        filled: true,
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.transparent),
          borderRadius: BorderRadius.circular(8),
        ),
      );
}
