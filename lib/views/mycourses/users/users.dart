import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfed/controllers/home_ctrl.dart';
import 'package:wellfed/models/user_model.dart';
import 'package:wellfed/utils/firebase.dart';
import 'package:wellfed/utils/loaders.dart';
import 'package:wellfed/views/mycourses/users/user_popup.dart';
import '../../../utils/methods.dart';
import '../../../utils/theme.dart';
import 'add_user_popup.dart';

class EUsers extends StatelessWidget {
  const EUsers({super.key, required this.branchId});
  final String? branchId;

  @override
  Widget build(BuildContext context) {
    print(branchId);
    return StreamBuilder<List<UserModel>>(
        stream: FBFireStore.users
            .where('linkedWith.${Get.find<HomeController>().userData?.eId}',
                isEqualTo: true)
            .where('bIds.$branchId', isEqualTo: true)
            .snapshots()
            .map((event) => event.docs
                .map((e) => UserModel.fromJson(e.id, e.data()))
                .toList()),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            debugPrint(snapshot.error.toString());
            return const Center(
              child: Text("Something went wrong!"),
            );
          }
          if (snapshot.hasData) {
            return EUsersWids(
              usersList: snapshot.data ?? [],
              branchId: branchId,
            );
          }
          return Center(
              child: loaderWave(color: Theme.of(context).primaryColor));
        });
  }
}

class EUsersWids extends StatefulWidget {
  const EUsersWids(
      {super.key, required this.usersList, required this.branchId});
  final List<UserModel> usersList;
  final String? branchId;
  @override
  State<EUsersWids> createState() => _EUsersWidsState();
}

class _EUsersWidsState extends State<EUsersWids> {
  final searchCtrl = TextEditingController();
  @override
  Widget build(BuildContext context) {
    // final size = MediaQuery.sizeOf(context);
    // bool small = size.width < mobileMinSize3;
    final filteredList = widget.usersList
        .where((element) =>
            element.name
                .toLowerCase()
                .contains(searchCtrl.text.toLowerCase()) ||
            element.email.toLowerCase().contains(searchCtrl.text.toLowerCase()))
        .toList();
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      // if (!small) _usersHeader(),
                      // const SizedBox(width: 12),
                      Flexible(child: _searchBox()),
                      const SizedBox(width: 12),
                    ],
                  ),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      minimumSize: const Size(100, 50),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6))),
                  onPressed: () => _addUserPopup(),
                  child: const Text(
                    "Add User",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          filteredList.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: Text("No linked user found!"),
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(12),
                  child: ListView.builder(
                    padding: const EdgeInsets.only(top: 12),
                    itemCount: filteredList.length,
                    shrinkWrap: true,
                    itemBuilder: (BuildContext context, int index) {
                      final user = filteredList[index];
                      return Card(
                        clipBehavior: Clip.hardEdge,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6)),
                        child: ListTile(
                          onTap: () => _uerDetailPopup(user),
                          leading: const Icon(CupertinoIcons.person_alt_circle),
                          title: Text(user.name),
                          subtitle: Text(user.email),
                          trailing: const CupertinoListTileChevron(),
                        ),
                      );
                    },
                  ),
                ),
        ],
      ),
    );
  }

  _uerDetailPopup(UserModel user) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(child: UserDetailPopup(user: user));
      },
    );
  }

  _addUserPopup() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
            child: AddUserDialog(
                userImportNoti: userImportNoti, bId: widget.branchId));
      },
    );
  }

  userImportNoti() {
    if (!context.mounted) return;
    showAppSnackBar(context, "Done", "User was imported successfully");
  }

  Padding _searchBox() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2.0),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Row(
          children: [
            Expanded(
              child: CupertinoSearchTextField(
                placeholderStyle: cuperPlaceHolderText,
                style: cuperSearchText,
                padding: const EdgeInsetsDirectional.fromSTEB(8, 12, 5.5, 12),
                controller: searchCtrl,
                placeholder: "Search",
                onChanged: (value) => setState(() {}),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
// class Small extends StatelessWidget {
//   const Small({super.key});
//   final TextEditingController searchCtrl;

//   @override
//   Widget build(BuildContext context) {
//     return const Placeholder();
//   }
// }
