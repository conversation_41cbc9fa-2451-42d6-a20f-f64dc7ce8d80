name: wellfed
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.1 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  firebase_core: ^3.15.2
  firebase_auth: ^5.7.0
  cloud_firestore: ^5.6.12
  firebase_storage: ^12.4.10
  cloud_functions: ^5.6.2
  google_fonts: 
  # firebase_ui_auth: ^1.4.3
  # file_picker: ^5.2.6
  # image_cropper: ^3.0.1
  pdf: ^3.10.3
  go_router: ^16.2.4
  webviewx: ^0.2.2
  get: ^4.6.5
  url_strategy: ^0.3.0
  loading_animation_widget: ^1.2.0+4
  # pointer_interceptor: ^0.9.3+4
  intl_phone_number_input: ^0.7.3+1
  flutter_staggered_grid_view: ^0.7.0
  awesome_snackbar_content: ^0.1.3
  percent_indicator: ^4.2.3
  chewie: ^1.7.0
  video_player: ^2.7.0
  url_launcher: ^6.1.12
  file_picker: ^10.3.3
  printing: ^5.11.0
  # screenshot: ^2.1.0
  dropdown_search: ^6.0.2


dependency_overrides:
  webview_flutter: ^4.0.2
  intl: ^0.18.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/google_fonts/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/google_fonts/Roboto-Bold.ttf
          weight: 700        
        - asset: assets/google_fonts/Roboto-Black.ttf
          weight: 900
    - family: Parisienne
      fonts:
        - asset: assets/google_fonts/Parisienne-Regular.ttf
          weight: 400
    - family: Montserrat
      fonts:
        - asset: assets/google_fonts/Montserrat-Bold.ttf
          weight: 700
        # - asset: assets/google_fonts/Montserrat-ExtraBold.ttf
        #   weight: 800
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
